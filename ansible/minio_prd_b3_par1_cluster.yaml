# Example: ansible-playbook minio_prd_b3_par1_cluster.yaml -i inventories/par1/ --diff -e "ansible_ssh_user=alexander-k<PERSON>ton<PERSON>" --check -K --ask-vault-pass
# Console: https://console.b3-par1.cnmtrcs.io/browser
# MinIO dashboard template for metrics v3: https://github.com/FedericoAntoniazzi/minio-grafana-dashboard-metrics-v3
- hosts: minio_b3
  become: yes
  vars_files:
    - minio_prd_b3_par1_cluster.yml.vault
  roles:
    - role: minio_init_drives
    - role: grafana_alloy
      vars:
        cluster: "b3"
        datacenter: "par1"
        datacenter_provider: "limestone"
        deploy_environment: "production"
    - role: minio
      vars:
        minio_vols: '"http://minio{1...4}/minio/data{1...18}"'
        minio_domain: 'b3-par1.cnmtrcs.io'
        nginx_vipv6: '2a14:fc80:0:3::3'
        nginx_vipv4: '***************'
        # minio1
        acme_issuing_host: 'fd14:e329:4823:0:401::5'
        minio_erasure_set_drive_count: 4
        minio_storage_class_standard: 'EC:1'
        minio_job_id: 'minio-b3'

# It runs once all hosts are set up.
- hosts: minio_b3
  become: yes
  vars_files:
    - minio_prd_b3_par1_cluster.yml.vault
  roles:
    - role: minio_auth
      when: ansible_play_hosts.index(inventory_hostname) == 0

