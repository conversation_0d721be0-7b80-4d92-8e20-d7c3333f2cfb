---
ansible_host: "fd14:e329:4823:0:401::1"

limestone_id: "LSN-D14170"

network_vlan10_v6: "2a14:fc80:0:0:401::1"
network_vlan10_v4: "*************"
network_vlan20_v6: "fd14:e329:4823:0:401::1"

network_external0_mac: "a0:36:9f:e8:9c:70"
network_external1_mac: "a0:36:9f:e8:9c:72"
network_internal0_mac: "7c:fe:90:72:6e:7a"
network_internal1_mac: "7c:fe:90:72:6e:7b"

# b2 cluster
minio_hostname: minio1
minio_bindaddrv6: fd14:e329:4823:0:401::1
nginx_bindaddrv6: 2a14:fc80:0:0:401::1

# An override of group variables just for the MinIO console.
# The console shows wrong numbers if cluster metrics are collected from all MinIO node.
# That's why we collect them only from minio1 here.
alloy_scrape_configs:
  # v3 is for our Grafana dashboards
  - name: "minio_v3"
    targets:
      - __address__: "127.0.0.1:9000"
        __metrics_path__: "/minio/metrics/v3"
        job: "minio-b2"
    scrape_interval: "30s"
    # v2 is needed only for the MinIO console
    # it has not migrated yet to v3
  - name: "minio_v2"
    targets:
      - __address__: "127.0.0.1:9000"
        __metrics_path__: "/minio/v2/metrics/cluster"
        job: "minio-b2"
    scrape_interval: "30s"
