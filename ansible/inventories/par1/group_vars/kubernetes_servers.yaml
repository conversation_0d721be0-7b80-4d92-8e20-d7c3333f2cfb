---
kubernetes_api_server_internal_vip_v6: "fd14:e329:4823:1:101::"
kubeadm_control_plane_endpoint: "api-cp2.cnmtrcs.io"

runc_version: "v1.3.0"
runc_checksum: "sha256:028986516ab5646370edce981df2d8e8a8d12188deaf837142a02097000ae2f2"
runc_install_dir: "/usr/local/bin"

containerd_version: "2.0.5"
containerd_archive_checksum: "sha256:88ab31f3e78e4d2fa12dcb933032122d11d441c83b79a89c6c8076f871e50df8"
containerd_install_dir: "/usr/local/bin"
containerd_config_dir: "/etc/containerd"

kubernetes_cluster_version_major: "1"
kubernetes_cluster_version_minor: "32"
kubernetes_cluster_version_patch: "4"
kubernetes_bootstrap_endpoint: "api-cp2.cnmtrcs.io"
kubernetes_ca_cert_hashes:
  - "sha256:ceee9044153a93f1600fbc8f1b85ab2a8b0979b5263702f7e5bd8e7293ca93cd"

cilium_install_dir: "/usr/local/bin"
cilium_cli_version: "0.18.0"
cilium_cli_archive_checksum: "sha256:3ac8bd270763e40a7853c73f8c7ec9e49707e1723801884a083dc25469b6b4ba"
cilium_hubble_version: "1.17.1"
cilium_hubble_archive_checksum: "sha256:1fa643076206df77be36fab8eaf900bd8bf9cf2dd479e13bdb2a5af98335e530"

kubernetes_static_routes:
  # JFK1
  - destination: 2620:a6:2001::/48
    gateway: fd14:e329:4823:1:301::1
  - destination: 10.0.0.0/16
    gateway: fd14:e329:4823:1:301::1

  # DFW1
  - destination: 2620:a6:2002::/48
    gateway: fd14:e329:4823:1:301::1
  - destination: 10.1.0.0/16
    gateway: fd14:e329:4823:1:301::1
