# "inside" and "outside" refer to machines which are within and beyond the
# firewall perimeter, respectively.
---
outside:
  children:
    monitoring:
      hosts:
        monitor-jfk1-1:
inside:
  children:
    kubernetes_servers:
      children:
        kubernetes_masters:
          hosts:
            cp1-master-jfk1-[1:3]:
        kubernetes_workers:
          hosts:
            cp1-worker-jfk1-[10:12]:
            cp1-worker-jfk1-[16:54]:
    minio:
      hosts:
        cpmin1-node-jfk1-[1:8]:
    kata:
      hosts:
        cp1-worker-jfk1-[10:12]:
        cp1-worker-jfk1-[16:54]:
    vpn:
      hosts:
        vpn-jfk1-1:
