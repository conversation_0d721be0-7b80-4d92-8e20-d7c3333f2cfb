---
kubernetes_api_server_internal_vip_v6: "fd2c:77db:603c:1:101::"
kubeadm_control_plane_endpoint: "api-cp1.cnmtrcs.io"

runc_version: "v1.1.12"
runc_checksum: "sha256:aadeef400b8f05645768c1476d1023f7875b78f52c7ff1967a6dbce236b8cbd8"
runc_install_dir: "/usr/local/bin"

containerd_version: "1.7.18"
containerd_archive_checksum: "sha256:a24b05b341c155a0ec367d3d0fd1d437c09a0261dffdecc0e44e9abbf2d02aca"
containerd_install_dir: "/usr/local/bin"
containerd_config_dir: "/etc/containerd"

kubernetes_cluster_version_major: "1"
kubernetes_cluster_version_minor: "30"
kubernetes_cluster_version_patch: "1"
kubernetes_bootstrap_endpoint: "api-cp1.cnmtrcs.io"
kubernetes_ca_cert_hashes:
  - "sha256:e6999d682eb7f111de5e8660b7f4e6513ab57dc76b1568d0d2a4d99dc54f9ada"

cilium_install_dir: "/usr/local/bin"
cilium_cli_version: "0.16.16"
cilium_cli_archive_checksum: "sha256:2d493a70e08d0df947a19d6065f40bd3886554a7fad30b02a7839aeee7400c02"
cilium_hubble_version: "1.16.0"
cilium_hubble_archive_checksum: "sha256:1322a897a299081c5dec5c7ce63c33906525c7c548f2d69d082b1cc0cb1336f2"

kubernetes_static_routes:
  # DFW1
  - destination: 2620:a6:2002::/48
    gateway: fd2c:77db:603c:1:301::1
  - destination: 10.1.0.0/16
    gateway: fd2c:77db:603c:1:301::1

  # PAR1
  - destination: 2a14:fc80::/48
    gateway: fd2c:77db:603c:1:301::1
  - destination: 10.2.0.0/16
    gateway: fd2c:77db:603c:1:301::1
