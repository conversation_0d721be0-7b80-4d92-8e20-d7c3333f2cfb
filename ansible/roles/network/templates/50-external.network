[Match]
Name=external

[Network]
{% if network_vlan10_v6 -%}
Address={{ network_vlan10_v6 }}/{{ network_vlan10_prefix_len_v6 }}
{% endif -%}
{% if network_vlan10_v4 -%}
Address={{ network_vlan10_v4 }}/{{ network_vlan10_prefix_len_v4 }}
{% endif -%}
IPv6AcceptRA=yes
# with "yes", host overrides in pfSense don't work
DNSSEC=no
DNS={{ network_dns }}
Domains={{ network_domain }}

{% if network_vlan10_gateway_v4 -%}
[Route]
Gateway={{ network_vlan10_gateway_v4 }}
{% endif -%}

{% if network_vlan10_gateway_v6 -%}
[Route]
Gateway={{ network_vlan10_gateway_v6 }}
{% endif -%}
