HOSTNAME={{ minio_hostname }}
MINIO_DOMAIN={{ minio_domain }}
# single quotes are required because the url may contain special characters (password)
MINIO_PROMETHEUS_URL='{{ minio_prom_url }}'
MINIO_PROMETHEUS_JOB_ID={{ minio_job_id }}
MINIO_PROMETHEUS_AUTH_TYPE=public
MINIO_API_ROOT_ACCESS=on
MINIO_ROOT_USER={{ minio_root_user }}
MINIO_ROOT_PASSWORD={{ minio_root_pass }}
MINIO_VOLUMES={{ minio_vols }}
MINIO_ERASURE_SET_DRIVE_COUNT={{ minio_erasure_set_drive_count }}
MINIO_STORAGE_CLASS_STANDARD={{ minio_storage_class_standard }}
MINIO_REGION={{ minio_region }}
MINIO_BROWSER_REDIRECT=false