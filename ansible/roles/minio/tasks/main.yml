- name: Import secrets
  include_vars:
    file: secrets.yml.vault

- name: Render systemd-networkd configuration
  ansible.builtin.template:
    src: 30-minio-loopback-vips.network.j2
    dest: /etc/systemd/network/30-minio-loopback-vips.network
    owner: root
    group: root
    mode: 0644

- name: Restart systemd-networkd
  ansible.builtin.systemd:
    name: systemd-networkd
    state: restarted

- name: install minio common packages
  apt:
    name:
      - parallel
      - nginx
      - ntp
      - iotop
      - iftop
      - bmon
      - iptraf-ng
    state: present
    update_cache: true

- name: Add IP address of all hosts to all hosts
  lineinfile:
    path: /etc/hosts
    line: "{{ hostvars[item].minio_bindaddrv6 }}    {{ hostvars[item].minio_hostname }}"
  when: hostvars[item].ansible_host is defined
  loop: "{{ groups[primary_group] }}"

- name: Ensure group minio exists
  ansible.builtin.group:
    name: minio
    state: present

- name: Add the user
  ansible.builtin.user:
    name: minio
    create_home: 1
    comment: User for Minio
    home: /minio
    shell: /sbin/nologin
    group: minio

- name: Create bin dir for minio
  ansible.builtin.file:
    path: /minio/bin
    state: directory
    owner: minio
    group: minio
    mode: '0770'

- name: Create cert dir for nginx
  ansible.builtin.file:
    path: "/etc/nginx/certs/{{ minio_domain }}"
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Download minio server
  ansible.builtin.get_url:
    # The version is locked because of that https://github.com/minio/object-browser/issues/3544
    # --- DO NOT UPDATE! DO NOT UPDATE! DO NOT UPDATE! ---
    url: https://dl.min.io/server/minio/release/linux-amd64/archive/minio.RELEASE.2025-04-22T22-12-26Z
    dest: /minio/bin/minio
    owner: minio
    group: minio
    mode: '0770'

- name: Download minio client
  ansible.builtin.get_url:
    # The version is locked because of that https://github.com/minio/object-browser/issues/3544
    # --- DO NOT UPDATE! DO NOT UPDATE! DO NOT UPDATE! ---
    url: https://dl.min.io/client/mc/release/linux-amd64/archive/mc.RELEASE.2025-04-16T18-13-26Z
    dest: /minio/bin/mc
    mode: '0770'
    owner: minio
    group: minio

- name: change ownership of homedir
  ansible.builtin.file:
    path: /minio
    state: directory
    recurse: no
    owner: minio
    group: minio
    mode: '0775'

- name: Copy minio rsyslog config
  ansible.builtin.copy:
    src: files/system/49-minio.conf
    dest: /etc/rsyslog.d/49-minio.conf
    owner: root
    group: root
    mode: '0644'

- name: Copy minio logrotate config
  ansible.builtin.copy:
    src: files/system/minio.logrotate
    dest: /etc/logrotate.d/minio
    owner: root
    group: root
    mode: '0644'

- name: deploy nginx config
  template:
    src: "minio_nginx_conf.j2"
    dest: /etc/nginx/sites-enabled/minio.conf
    owner: root
    group: root
    mode: 0644

- name: deploy .htpasswd to protect MinIO Console
  ansible.builtin.copy:
    src: "system/.htpasswd"
    dest: /etc/nginx/sites-enabled/.htpasswd
    owner: root
    group: root
    mode: '0644'

- name: Remove default nginx config
  ansible.builtin.file:
    path: /etc/nginx/sites-enabled/default
    state: absent

- name: deploy DH nginx cert
  template:
    src: files/system/dhparam.pem
    dest: /etc/nginx/certs/dhparam.pem
    owner: root
    group: root
    mode: 0644

- name: Copy nginx placeholder certs
  ansible.builtin.copy:
    src: files/certs/{{ item }}
    dest: /etc/nginx/certs/{{ minio_domain }}/{{ item }}
    owner: root
    group: root
    force: false
  with_items:
    - fullchain.pem
    - privkey.pem

- name: Issue Let's Encrypt wildcard certificate (only on issuing server)
  block:
    - name: Install acme.sh
      shell: |
        curl https://get.acme.sh | sh -s email={{ acme_email }}
      args:
        creates: "/root/.acme.sh/acme.sh"

    - name: Upload deploy hook script
      copy:
        src: deploy-hook.sh
        dest: /root/.acme.sh/deploy-hook.sh
        mode: '0755'

    - name: "Issue certificate (it can take many minutes; please wait)"
      shell: |
        export CF_Account_ID="{{ cf_account_id }}"
        export CF_Token="{{ cf_token }}"
        /root/.acme.sh/acme.sh --issue -d {{ minio_domain }} -d '*.{{ minio_domain }}' --dns dns_cf --post-hook /root/.acme.sh/deploy-hook.sh
      args:
        creates: "/root/.acme.sh/{{ minio_domain }}_ecc"

  when: ansible_host == acme_issuing_host

- name: Copy SSL deployment script
  template:
    src: download_and_deploy_ssl.sh.j2
    dest: /minio/download_and_deploy_ssl.sh
    mode: 0755

- name: Add a daily cron job for updated SSL files deployment
  cron:
    name: "Download and deploy MinIO SSL certs"
    job: "/minio/download_and_deploy_ssl.sh"
    user: root
    minute: "0"
    hour: "3"

- name: Reload service rsyslogd
  ansible.builtin.systemd:
    name: rsyslog.service
    state: restarted

- name: Start nginx service
  ansible.builtin.systemd:
    name: nginx.service
    state: started

- name: Copy minio service
  ansible.builtin.copy:
    src: files/system/minio.service
    dest: /etc/systemd/system/minio.service
    owner: root
    group: root
    mode: '0644'

- name: Copy sysctl.conf mods
  ansible.builtin.copy:
    src: files/system/60-netspeed.conf
    dest: /etc/sysctl.d/60-netspeed.conf
    owner: root
    group: root
    mode: '0644'

- name: Apply custom sysctl settings
  command: sysctl -p

- name: create minio config file
  template:
    src: minio.env.j2
    dest: /etc/default/minio
    owner: minio
    group: minio
    mode: '0640'
    backup: true

- name: Change minio config perms
  ansible.builtin.file:
    path: /etc/default/minio
    owner: minio
    group: minio
    mode: '0640'

- name: Enable service nginx
  ansible.builtin.systemd:
    name: nginx
    enabled: true
    masked: no
    state: started

- name: Enable service ntpsec
  ansible.builtin.systemd:
    name: ntpsec
    enabled: true
    masked: no
    state: started

- name: Enable service minio
  ansible.builtin.systemd:
    name: minio
    enabled: true
    masked: no
    state: started

- name: Reload service nginx
  ansible.builtin.systemd:
    name: nginx.service
    # reloaded doesn't enable the 444 port for some reason
    state: restarted

- name: Run MinIO SSL deployment script
  ansible.builtin.shell: /minio/download_and_deploy_ssl.sh

- name: Reload service minio
  ansible.builtin.systemd:
    name: minio.service
    state: restarted

    #- name: Unconditionally reboot the machine with all defaults
    #  ansible.builtin.reboot:
