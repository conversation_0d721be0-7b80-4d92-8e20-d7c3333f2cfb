minio_vols: '"http://minio{1...4}/minio/data{1...14} http://minio{5...8}/minio/data{1...14}"'
# directly affects cluster performance; the smaller, the faster
# can't be changed after cluster creation
minio_erasure_set_drive_count: 8
# affects redundancy; the bigger, the safer
minio_storage_class_standard: 'EC:2'
minio_root_user: 'InfraAdmin'
# no reason to override this; this is a default MinIO value that works fine in all s3 clients
minio_region: us-east-1

acme_email: "<EMAIL>"
# The ID from the account dashboard: https://dash.cloudflare.com/b8700cfea9d3ed52c7ae3b881a5e7782/home
cf_account_id: "b8700cfea9d3ed52c7ae3b881a5e7782"
