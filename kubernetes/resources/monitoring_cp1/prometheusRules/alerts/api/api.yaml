apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: api4
  namespace: api4
  labels:
    release: prometheus
spec:
  groups:
    - name: errors
      rules:
        - alert: Errors500
          expr: |-
            sum(increase(non_ok_responses_total{service="$deployment", code=~"5.."}[1m])) by (pod, code) > 0
          for: 2m
          labels:
            severity: critical
          annotations:
            summary: "{{$labels.pod}} has 5xx errors."
            description: "{{$labels.pod}} has 5xx errors."

    - name: API4
      rules:
        # Pods not Ready >1 hour
        - alert: API4PodsNotReady
          expr: |-
            kube_statefulset_status_replicas{namespace="api4"} == 0
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: "An API4 pod {{$labels.statefulset}} has been in a NotReady state for > 1 hour."
            description: "An API4 pod {{$labels.statefulset}} has been in a NotReady state for > 1 hour."
        - alert: API4PodsNotReady2
          expr: |-
            kube_deployment_status_replicas{namespace="api4"} == 0
          for: 1h
          labels:
            severity: warning
          annotations:
            summary: "An API4 pod {{$labels.deployment}} has been in a NotReady state for > 1 hour."
            description: "An API4 pod {{$labels.deployment}} has been in a NotReady state for > 1 hour."
        # API4 Pro
        - alert: SingleInstanceAPI4Pro
          expr: |-
            sum by(namespace) (kube_statefulset_status_replicas_ready{namespace="api4", statefulset=~"api4-\\d"}) == 1
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "API4 Pro is operating without redundancy."
            description: "API4 Pro has been operating on a single instance for 5 minutes."
        - alert: API4ProDown
          expr: |-
            sum by(namespace) (kube_statefulset_status_replicas_ready{namespace="api4", statefulset=~"api4-\\d"}) == 0
          for: 0m
          labels:
            severity: fatal
          annotations:
            summary: "API4 Pro is down."
            description: "Zero API4 Pro pods are in a ready state."
        - alert: API4Pro_Books_Kafka_ConsumerLag_High
          expr: |-
            # This excludes two exchanges which are well-accepted to frequently lag
            # 39=kucoin, 49=mexc
              max by (namespace) (
                api_streaming_books_pipeline_lag{service="streaming-books-api-ingress",topic!~"books_39.proto|books_49.proto"}
              )
            >
              60
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "One of the API pods is lagging consuming data from kafka-books."
            description: "Any clients streaming books data from [one of the] API will see delayed data."
        - alert: API4Pro_Trades_Kafka_ConsumerLag_High
          expr: |-
            api_ws_trades_pipeline_lag{service="api4-ingress"} > 600
          for: 15m
          labels:
            severity: critical
          annotations:
            summary: "Trades Kafka Consumer Lag is High. Slacks : #team-platform, #cm-internal-incidents. Possible issues : APIv4 processing impacted / pods restarted"
            description: "Trades Kafka Consumer Lag is High."
        - alert: API4Pro_Candles_Kafka_ConsumerLag_High
          expr: |-
            api_ws_candles_pipeline_lag{service="api4-ingress"} > 120
          for: 15m
          labels:
            severity: critical
          annotations:
            summary: "Candles Kafka Consumer Lag is High. Slacks : #team-platform, #cm-internal-incidents. Possible issues : APIv4 processing impacted / pods restarted"
            description: "Candles Kafka Consumer Lag is High."
        - alert: Market_Quotes_Kafka_ConsumerLag_High
          expr: |-
            max by (namespace) (
                api_ws_quotes_pipeline_lag{service="api4-ingress"}
              )
            >
              360
          for: 15m
          labels:
            severity: critical
          annotations:
            summary: "Quotes Kafka Lag is High. Slacks : #team-market-data, #cm-incidents."
            description: "Quotes Kafka Lag is High."
        - alert: API4Pro_PairQ_Kafka_ConsumerLag_High
          expr: |-
            max by (namespace) (api_ws_pair_quotes_pipeline_lag{service="api4-ingress"}) > 300
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pair Quotes Kafka Consumer Lag is High."
            description: "Clients may experience pair quote delays."
        # API4 Community
        - alert: SingleInstanceAPI4Community
          expr: |-
            sum by(namespace) (kube_statefulset_status_replicas_ready{namespace="api4", statefulset=~"community-api4-\\d"}) == 1
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "API4 Community is operating without redundancy."
            description: "API4 Community has been operating on a single instance for 5 minutes."
        - alert: API4CommunityDown
          expr: |-
            sum by(namespace) (kube_statefulset_status_replicas_ready{namespace="api4", statefulset=~"community-api4-\\d"}) == 0
          for: 0m
          labels:
            severity: fatal
          annotations:
            summary: "API4 Community is down."
            description: "Zero API4 Community pods are in a ready state."
        # API Docs
        - alert: SingleInstanceAPI4Docs
          expr: |-
            kube_deployment_status_replicas_ready{namespace="api4", deployment="api4-docs"} == 1
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "API4 Docs are operating without redundancy."
            description: "API4 Docs have been operating on a single instance for 5 minutes."
        - alert: API4DocsDown
          expr: |-
            kube_deployment_status_replicas_ready{namespace="api4", deployment="api4-docs"} == 0
          for: 0m
          labels:
            severity: fatal
          annotations:
            summary: "API4 Docs are down."
            description: "Zero API4 Docs pods are in a ready state."
    - name: Statistic
      rules:
        # Statistic didn't calculate for 2 minutes
        - alert: StatisticNotCalculated_10m
          expr: |-
            sum(changes(api_statistics_update_latency_seconds_count{namespace='api4', statistics_name!~"eod-market-candles|future-metadata|market|option-metadata|spot-metadata|index-constituents"}[15m])) by (statistics_name, pod, namespace) == 0
          for: 2m
          labels:
            severity: critical
          annotations:
            summary: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes."
            description: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: StatisticNotCalculated_50m
          expr: |-
           sum(changes(api_statistics_update_latency_seconds_count{namespace='api4', statistics_name=~"future-metadata|market|option-metadata|spot-metadata"}[50m])) by (statistics_name, pod, namespace) == 0
          for: 15m
          labels:
            severity: critical
          annotations:
            summary: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes."
            description: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: StatisticNotCalculated_60m
          expr: |-
            sum(changes(api_statistics_update_latency_seconds_count{namespace='api4', statistics_name=~"index-constituents"}[60m])) by (statistics_name, pod, namespace) == 0
          for: 15m
          labels:
            severity: critical
          annotations:
            summary: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes."
            description: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: StatisticNotCalculated_1455m
          expr: |-
            sum(changes(api_statistics_update_latency_seconds_count{namespace='api4', statistics_name=~"eod-market-candles"}[1455m])) by (statistics_name, pod, namespace) == 0
          for: 30m
          labels:
            severity: critical
          annotations:
            summary: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes."
            description: "Pod {{$labels.pod}} Statistic {{$labels.statistics_name}} didn't calculate for 2 minutes. https://coinmetrics.atlassian.net/browse/PLAT-706"
        # No data for 10 minutes

        - alert: Statistic api4-1-0-address-tagging NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"address-tagging"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-asset-chains NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"asset-chains"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-defi NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"defi"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-eod-market-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"eod-market-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-exchange-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"exchange-asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-exchange-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"exchange-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-future-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"future-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-index NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"index"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-index-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"index-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-institution-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"institution-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-market NO DATA
          expr: |-
            absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"market-candles|market-cold-books|market-defi|market-funding-rates-predicted|market-funding-rates|market-future-ticker|market-hot-books|market-liquidations|market-metrics|market-open-interest|market-option-ticker|market-trades"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-mempool-feerates NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"mempool-feerates"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-mining-pool-tips-summary NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"mining-pool-tips-summary"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-option-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"option-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-pair-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"pair-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-pair-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"pair-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-spot-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"spot-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-transaction-tracker NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"transaction-tracker"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-1-0-udm-v2-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-1-0", statistics_name=~"udm-v2-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-address-tagging NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"address-tagging"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-asset-chains NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"asset-chains"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-defi NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"defi"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-eod-market-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"eod-market-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-exchange-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"exchange-asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-exchange-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"exchange-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-future-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"future-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-index NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"index"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-index-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"index-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-institution-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"institution-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-market NO DATA
          expr: |-
            absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"market-candles|market-cold-books|market-defi|market-funding-rates-predicted|market-funding-rates|market-future-ticker|market-hot-books|market-liquidations|market-metrics|market-open-interest|market-option-ticker|market-trades"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-mempool-feerates NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"mempool-feerates"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-mining-pool-tips-summary NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"mining-pool-tips-summary"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-option-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"option-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-pair-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"pair-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-pair-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"pair-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-spot-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"spot-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-transaction-tracker NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"transaction-tracker"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-2-0-udm-v2-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-2-0", statistics_name=~"udm-v2-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-address-tagging NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"address-tagging"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-asset-chains NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"asset-chains"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-defi NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"defi"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-eod-market-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"eod-market-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-exchange-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"exchange-asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-exchange-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"exchange-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-future-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"future-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-index NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"index"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-index-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"index-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-institution-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"institution-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-market NO DATA
          expr: |-
            absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"market-candles|market-cold-books|market-defi|market-funding-rates-predicted|market-funding-rates|market-future-ticker|market-hot-books|market-liquidations|market-metrics|market-open-interest|market-option-ticker|market-trades"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-mempool-feerates NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"mempool-feerates"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-mining-pool-tips-summary NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"mining-pool-tips-summary"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-option-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"option-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-pair-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"pair-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-pair-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"pair-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-spot-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"spot-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-transaction-tracker NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"transaction-tracker"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic api4-3-0-udm-v2-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="api4-3-0", statistics_name=~"udm-v2-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-address-tagging NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"address-tagging"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-asset-chains NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"asset-chains"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-defi NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"defi"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-eod-market-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"eod-market-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-exchange-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"exchange-asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-exchange-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"exchange-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-future-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"future-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-index NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"index"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-index-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"index-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-institution-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"institution-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-market NO DATA
          expr: |-
            absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"market-candles|market-cold-books|market-defi|market-funding-rates-predicted|market-funding-rates|market-future-ticker|market-hot-books|market-liquidations|market-metrics|market-open-interest|market-option-ticker|market-trades"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-mempool-feerates NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"mempool-feerates"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-mining-pool-tips-summary NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"mining-pool-tips-summary"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-option-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"option-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-pair-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"pair-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-pair-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"pair-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-spot-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"spot-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-transaction-tracker NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"transaction-tracker"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-1-0-udm-v2-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-1-0", statistics_name=~"udm-v2-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-address-tagging NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"address-tagging"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-asset-chains NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"asset-chains"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-defi NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"defi"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-eod-market-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"eod-market-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-exchange-asset-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"exchange-asset-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-exchange-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"exchange-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-future-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"future-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-index NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"index"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-index-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"index-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-institution-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"institution-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-market NO DATA
          expr: |-
            absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"market-candles|market-cold-books|market-defi|market-funding-rates-predicted|market-funding-rates|market-future-ticker|market-hot-books|market-liquidations|market-metrics|market-open-interest|market-option-ticker|market-trades"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-mempool-feerates NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"mempool-feerates"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-mining-pool-tips-summary NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"mining-pool-tips-summary"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-option-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"option-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-pair-candles NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"pair-candles"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-pair-metrics NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"pair-metrics"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-spot-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"spot-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-transaction-tracker NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"transaction-tracker"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        - alert: Statistic community-api4-2-0-udm-v2-metadata NO DATA
          expr: |-
           absent(api_statistics_update_latency_seconds_count{namespace='api4', pod="community-api4-2-0", statistics_name=~"udm-v2-metadata"}) ==  1
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA"
            description: "Pod {$labels.pod} Statistic {$labels.statistics_name} NO DATA. https://coinmetrics.atlassian.net/browse/PLAT-706"
        
