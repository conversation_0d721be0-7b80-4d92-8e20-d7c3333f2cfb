---
apiVersion: monitoring.coreos.com/v1
kind: PodMonitor
metadata:
  name: frontend
  namespace: monitoring
  labels:
    release: prometheus
spec:
  podMetricsEndpoints:
    - port: "prometheus"
  namespaceSelector:
    matchNames:
      - frontend
  podTargetLabels:
    - app.kubernetes.io/name
  selector:
    matchExpressions:
      - key: app.kubernetes.io/name
        operator: In
        values:
          - cm-coverage-ui
