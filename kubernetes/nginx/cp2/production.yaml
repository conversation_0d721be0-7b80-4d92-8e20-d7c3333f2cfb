---
fullnameOverride: "nginx-production"
controller:
  ingressClass:
    create: true
    name: nginx-production
    setAsDefaultIngress: false
  enableSnippets: true
  enableCertManager: false
  replicaCount: 2
  pod:
    extraLabels:
      nginx.coinmetrics.io/class: production
  resources:
    requests:
      cpu: 1
  affinity:
    podAntiAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        - labelSelector:
            matchExpressions:
              - key: nginx.coinmetrics.io/class
                operator: In
                values:
                  - production
          topologyKey: kubernetes.io/hostname
  service:
    allocateLoadBalancerNodePorts: false
    annotations:
      io.cilium/lb-ipam-ips: "***************,2a14:fc80:0:3::11"
    externalTrafficPolicy: Local
    extraLabels:
      coinmetrics.io/announce: "true"
    ipFamilyPolicy: RequireDualStack
    type: LoadBalancer
  config:
    entries:
      ssl-protocols: TLSv1.2 TLSv1.3
      http2: "true"
      worker-processes: "4"
      worker-connections: "10240"
      # https://www.cloudflare.com/ips/
      # load balancer IPs: https://gitlab.com/coinmetrics/ops/load-balancer/-/blob/master/ansible/hosts.yml#L4
      set-real-ip-from: ************/22,************/22,**********/22,**********/13,**********/14,*************/18,**********/22,************/18,***********/15,**********/13,************/20,************/20,************/20,*************/22,************/17,2400:cb00::/32,2606:4700::/32,2803:f800::/32,2405:b500::/32,2405:8100::/32,2c0f:f248::/32,2a06:98c0::/29,**************,**************,************
      real-ip-header: CF-Connecting-IP
      server-tokens: "false"
      lb-method: least_conn
      log-format: |
        $remote_addr - $remote_user [$time_iso8601]
        "$request_method $host$request_uri $server_protocol"
        $status $body_bytes_sent $request_time "$http_referer"
        "$http_user_agent" $http_cf_ray

prometheus:
  create: true
  service:
    create: true
  serviceMonitor:
    create: true
    labels:
      release: prometheus
