---
runners:
  config: |
    [[runners]]
      [runners.cache]
        Type = "s3"
        Path = "/"
        Shared = true
        [runners.cache.s3]
          ServerAddress = "s3-jfk1.coinmetrics.io"
          BucketLocation = "prd-jfk1"
          BucketName = "gitlab-cache"
          Insecure = false
      [runners.kubernetes]
        image = "ubuntu:24.04"
        pull_policy = ["always"]
        privileged = false

        cpu_request = "2"
        cpu_request_overwrite_max_allowed = "32"
        cpu_limit = "2"
        cpu_limit_overwrite_max_allowed = "32"

        memory_request = "4Gi"
        memory_request_overwrite_max_allowed = "64Gi"
        memory_limit = "4Gi"
        memory_limit_overwrite_max_allowed = "64Gi"

        ephemeral_storage_request = "8Gi"
        ephemeral_storage_limit = "32Gi"

        [runners.kubernetes.pod_labels]
          "coinmetrics.io/allow-minio" = ""
  secret: gitlab-runner
  cache:
    secretName: gitlab-runner-minio-cache
  concurrent: 10
