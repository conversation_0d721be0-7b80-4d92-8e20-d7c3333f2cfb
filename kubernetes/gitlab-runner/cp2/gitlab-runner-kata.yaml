---
runners:
  config: |
    [[runners]]
      [runners.kubernetes]
        image = "ubuntu:24.04"
        pull_policy = [ "always" ]
        privileged = true

        cpu_request = "2"
        cpu_request_overwrite_max_allowed = "32"
        cpu_limit = "2"
        cpu_limit_overwrite_max_allowed = "32"

        service_cpu_request = "1"
        service_cpu_request_overwrite_max_allowed = "32"
        service_cpu_limit = "1"
        service_cpu_limit_overwrite_max_allowed = "32"

        memory_request = "4Gi"
        memory_request_overwrite_max_allowed = "64Gi"
        memory_limit = "4Gi"
        memory_limit_overwrite_max_allowed = "64Gi"

        service_memory_request = "2Gi"
        service_memory_request_overwrite_max_allowed = "32Gi"
        service_memory_limit = "2Gi"
        service_memory_limit_overwrite_max_allowed = "32Gi"

        ephemeral_storage_request = "8Gi"
        ephemeral_storage_limit = "8Gi"

        service_ephemeral_storage_request = "8Gi"
        service_ephemeral_storage_limit = "8Gi"

        runtime_class_name = "kata"
        dns_policy = "default"
        allowed_services = [
          "registry.gitlab.com/coinmetrics/infrastructure/datacenter/gitlab-runner-docker-service:latest",
          "postgres:13.5-alpine"
        ]

        [runners.kubernetes.pod_labels]
          "coinmetrics.io/allow-minio" = ""
          "coinmetrics.io/allow-vlan10" = ""

        [runners.kubernetes.node_selector]
          "katacontainers.io/kata-runtime" = "true"

        [[runners.kubernetes.volumes.empty_dir]]
          name = "var-run"
          mount_path = "/var/run"
          medium = "Memory"
  secret: gitlab-runner-kata
