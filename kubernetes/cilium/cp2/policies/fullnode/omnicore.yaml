---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: omnicore
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: omnicore
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: omnicore-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: omnicore
              protocol: TCP
  egress:
    - toEntities:
        - world
