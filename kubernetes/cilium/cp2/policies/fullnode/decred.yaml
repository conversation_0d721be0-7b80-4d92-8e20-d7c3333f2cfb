---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: decred
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: decred
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: decred-rpc
              protocol: TCP
            - port: decred
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: decred-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: decred
              protocol: TCP
  egress:
    - toEntities:
        - world
