---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: algorand-postgres
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: algorand
      app.kubernetes.io/component: postgres
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: conduit
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: indexer
      toPorts:
        - ports:
            - port: postgres
              protocol: TCP
