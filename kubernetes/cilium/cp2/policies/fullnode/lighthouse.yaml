---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: lighthouse
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: lighthouse
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: network-data-metrics
      toPorts:
        - ports:
            - port: eth2-rpc-http
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: eth2
              protocol: TCP
            - port: eth2
              protocol: UDP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: geth
      toPorts:
        - ports:
            - port: eth-authrpc
              protocol: TCP
    - toEntities:
        - world
