---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: algorand-node
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: algorand
      app.kubernetes.io/component: node
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: conduit
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: indexer
      toPorts:
        - ports:
            - port: algorand-api
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: algorand
              protocol: TCP
  egress:
    - toEntities:
        - world
