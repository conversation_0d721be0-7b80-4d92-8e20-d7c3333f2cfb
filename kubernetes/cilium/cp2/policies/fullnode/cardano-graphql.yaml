---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cardano-graphql
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cardano
      app.kubernetes.io/component: graphql
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: graphql
              protocol: TCP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: postgres
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: hasura
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: ogmios
    - toEntities:
        - world
      toPorts:
        - ports:
            - port: "433"
              protocol: TCP
