---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cardano-db-sync
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cardano
      app.kubernetes.io/component: db-sync
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: postgres
    - toEntities:
        - world
