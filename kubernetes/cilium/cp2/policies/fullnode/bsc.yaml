---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: bsc
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: bsc
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: network-data-metrics
      toPorts:
        - ports:
            - port: bsc-rpc-http
              protocol: TCP
  egress:
    - toEntities:
        - world
