---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: bitcoin
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: bitcoin
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: bitcoin-rpc
              protocol: TCP
            - port: bitcoin-zmq
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: bitcoin-rpc
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: farum
      toPorts:
        - ports:
            - port: bitcoin-zmq
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: bitcoin
              protocol: TCP
  egress:
    - toEntities:
        - world
