---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: dogecoin
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: dogecoin
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: doge-rpc
              protocol: TCP
            - port: doge
              protocol: TCP
            - port: doge-zmq
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: doge-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: doge
              protocol: TCP
  egress:
    - toEntities:
        - world
