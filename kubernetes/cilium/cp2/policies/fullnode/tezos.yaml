---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: tezos
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: tezos
      app.kubernetes.io/component: node
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos-indexer
            k8s:app.kubernetes.io/component: sync
      toPorts:
        - ports:
            - port: tezos-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: tezos-p2p
              protocol: TCP
  egress:
    - toEntities:
        - world
