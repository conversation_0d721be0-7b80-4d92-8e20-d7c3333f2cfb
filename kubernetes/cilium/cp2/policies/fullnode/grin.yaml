---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: grin
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: grin
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: grin-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: grin
              protocol: TCP
  egress:
    - toEntities:
        - world
