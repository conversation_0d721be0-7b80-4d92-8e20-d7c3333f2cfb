---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: geth
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: geth
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: defi
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: network-data-metrics
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: tagging
      toPorts:
        - ports:
            - port: eth-rpc-http
              protocol: TCP
    # Other fullnodes, like Lighthouse, L2 EVMs, need to talk to both RPC and auth RPC APIs
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
      toPorts:
        - ports:
            - port: eth-rpc-http
              protocol: TCP
        - ports:
            - port: eth-authrpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: eth
              protocol: TCP
            - port: eth
              protocol: UDP
  egress:
    - toEntities:
        - world
