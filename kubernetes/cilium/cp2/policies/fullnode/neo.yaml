---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: neo
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: neo
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: neo-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: neo
              protocol: TCP
  egress:
    - toEntities:
        - world
