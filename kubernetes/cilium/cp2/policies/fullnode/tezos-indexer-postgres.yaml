---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: tezos-indexer-postgres
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: tezos-indexer
      app.kubernetes.io/component: postgres
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos-indexer
            k8s:app.kubernetes.io/component: api
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos-indexer
            k8s:app.kubernetes.io/component: sync
      toPorts:
        - ports:
            - port: tezos-psql
              protocol: TCP
