---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: monero
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: monero
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: monero-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: monero
              protocol: TCP
  egress:
    - toEntities:
        - world
