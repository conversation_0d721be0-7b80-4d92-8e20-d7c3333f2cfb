---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: coregeth
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: coregeth
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: network-data-metrics
      toPorts:
        - ports:
            - port: eth-rpc-http
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: eth
              protocol: TCP
            - port: eth-udp
              protocol: UDP
  egress:
    - toEntities:
        - world
