---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: polkadot
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: polkadot
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
      toPorts:
        - ports:
            - port: dot-rpc-http
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: dot-sas-http
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: dot
              protocol: TCP
  egress:
    - toEntities:
        - world
