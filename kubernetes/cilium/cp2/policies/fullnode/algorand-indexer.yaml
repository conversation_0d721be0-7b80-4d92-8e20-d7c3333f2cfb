---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: algorand-indexer
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: algorand
      app.kubernetes.io/component: indexer
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
            k8s:app.kubernetes.io/name: atlasv2
      toPorts:
        - ports:
            - port: indexer-api
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
            k8s:app.kubernetes.io/name: blockchain-prometheus-monitoring
      toPorts:
        - ports:
            - port: indexer-api
              protocol: TCP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: node
      toPorts:
        - ports:
            - port: algorand-api
              protocol: TCP
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: postgres
      toPorts:
        - ports:
            - port: postgres
              protocol: TCP
