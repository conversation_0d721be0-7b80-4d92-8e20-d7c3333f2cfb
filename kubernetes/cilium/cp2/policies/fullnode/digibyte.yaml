---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: digibyte
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: digibyte
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: digibyte-rpc
              protocol: TCP
            - port: digibyte-zmq
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: digibyte-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: digibyte
              protocol: TCP
  egress:
    - toEntities:
        - world
