---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: nem
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: nem
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: nem
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: nem
              protocol: TCP
  egress:
    - toEntities:
        - world
