---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: tezos-indexer-api
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: tezos-indexer
      app.kubernetes.io/component: api
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: tez-indexer-api
              protocol: TCP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos-indexer
            k8s:app.kubernetes.io/component: postgres
