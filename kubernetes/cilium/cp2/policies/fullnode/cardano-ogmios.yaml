---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cardano-ogmios
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cardano
      app.kubernetes.io/component: ogmios
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: graphql
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: graphql-background
      toPorts:
        - ports:
            - port: ogmios
              protocol: TCP
