---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: bitcoin-cash
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: bitcoin-cash
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: btc-cash-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: btc-cash
              protocol: TCP
  egress:
    - toEntities:
        - world
