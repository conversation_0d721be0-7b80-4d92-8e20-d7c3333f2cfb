---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: verge
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: verge
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: verge-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: verge
              protocol: TCP
  egress:
    - toEntities:
        - world
