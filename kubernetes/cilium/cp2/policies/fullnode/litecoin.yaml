---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: litecoin
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: litecoin
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: litecoin-rpc
              protocol: TCP
            - port: litecoin-zmq
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: litecoin-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: litecoin
              protocol: TCP
  egress:
    - toEntities:
        - world
