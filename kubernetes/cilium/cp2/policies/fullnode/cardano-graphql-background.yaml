---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cardano-graphql-background
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cardano
      app.kubernetes.io/component: graphql-background
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: postgres
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: hasura
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: cardano
            k8s:app.kubernetes.io/component: ogmios
    - toEntities:
        - world
