---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: avalanche-rosetta
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: avalanche
      app.kubernetes.io/component: rosetta
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: rosetta
              protocol: TCP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: avalanche
            k8s:app.kubernetes.io/component: node
