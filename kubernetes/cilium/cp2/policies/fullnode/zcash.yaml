---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: zcash
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: zcash
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: zcash-rpc
              protocol: TCP
        - ports:
            - port: zcash-zmq
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: zcash
              protocol: TCP
  egress:
    - toEntities:
        - world
