---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: dfinity-rosetta
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: dfinity-rosetta
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: dfinity-rosetta
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: dfinity-rosetta
              protocol: TCP
  egress:
    - toEntities:
        - world
