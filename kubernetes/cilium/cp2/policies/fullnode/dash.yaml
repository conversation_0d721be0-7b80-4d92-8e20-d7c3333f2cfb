---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: dash
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: dash
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: dash-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: dash
              protocol: TCP
  egress:
    - toEntities:
        - world
