---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: bitcoin-gold
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: bitcoin-gold
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
      toPorts:
        - ports:
            - port: bitcoin-g-rpc
              protocol: TCP
            - port: bitcoin-g
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: bitcoin-g-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: bitcoin-g
              protocol: TCP
  egress:
    - toEntities:
        - world
