---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: vertcoin
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: vertcoin
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: vertcoin-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: vertcoin
              protocol: TCP
  egress:
    - toEntities:
        - world
