---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: tezos-indexer-sync
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: tezos-indexer
      app.kubernetes.io/component: sync
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos-indexer
            k8s:app.kubernetes.io/component: postgres
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: tezos
            k8s:app.kubernetes.io/component: node
