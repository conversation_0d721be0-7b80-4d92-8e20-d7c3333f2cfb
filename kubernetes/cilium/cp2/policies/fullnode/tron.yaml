---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: tron
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: tron
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: tron-http
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: tron-tcp
              protocol: TCP
            - port: tron-udp
              protocol: UDP
  egress:
    - toEntities:
        - world
