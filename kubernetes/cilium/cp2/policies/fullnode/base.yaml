---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: base
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: base
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: base-rpc
              protocol: TCP
  egress:
    - toEntities:
        - world
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: geth
      toPorts:
        - ports:
            - port: eth-rpc-http
              protocol: TCP
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: lighthouse
      toPorts:
        - ports:
            - port: eth2-rpc-http
              protocol: TCP
