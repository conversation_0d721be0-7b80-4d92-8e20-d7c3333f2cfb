---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: avalanche-node
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: avalanche
      app.kubernetes.io/component: node
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: atlas
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: fullnode
            k8s:app.kubernetes.io/name: avalanche
            k8s:app.kubernetes.io/component: rosetta
      toPorts:
        - ports:
            - port: avalanche-rpc
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: avalanche-p2p
              protocol: TCP
  egress:
    - toEntities:
        - world
