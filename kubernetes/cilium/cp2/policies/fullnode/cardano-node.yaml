---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cardano-node
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cardano
      app.kubernetes.io/component: node
  ingress:
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: cardano
              protocol: TCP
  egress:
    - toEntities:
        - world
