---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: algorand-conduit
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: algorand
      app.kubernetes.io/component: conduit
  ingress:
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: conduit-api
              protocol: TCP
  egress:
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: node
      toPorts:
        - ports:
            - port: "8080"
              protocol: TCP
    - toEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: algorand
            k8s:app.kubernetes.io/component: postgres
      toPorts:
        - ports:
            - port: postgres
              protocol: TCP
    - toEntities:
        - world
