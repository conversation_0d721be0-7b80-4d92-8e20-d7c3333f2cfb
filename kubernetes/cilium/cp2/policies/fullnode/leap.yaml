---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: leap
  namespace: fullnode
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: leap
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-exporter
        - matchLabels:
            k8s:io.kubernetes.pod.namespace: blockchain-monitoring
      toPorts:
        - ports:
            - port: leap-api
              protocol: TCP
            - port: leap-history
              protocol: TCP
    - fromEntities:
        - world
      toPorts:
        - ports:
            - port: leap
              protocol: TCP
  egress:
    - toEntities:
        - world
