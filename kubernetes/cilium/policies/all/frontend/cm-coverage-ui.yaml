---
apiVersion: cilium.io/v2
kind: CiliumNetworkPolicy
metadata:
  name: cm-coverage-ui
  namespace: frontend
spec:
  endpointSelector:
    matchLabels:
      app.kubernetes.io/name: cm-coverage-ui
  ingress:
    - fromEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: prometheus
            k8s:io.kubernetes.pod.namespace: monitoring
      toPorts:
        - ports:
            - port: "prometheus"
              protocol: TCP
    - fromEndpoints:
        - matchLabels:
            k8s:app.kubernetes.io/name: nginx-ingress
            k8s:io.kubernetes.pod.namespace: nginx-production
      toPorts:
        - ports:
            - port: http
              protocol: TCP
  egress:
    - toEntities:
        - world
