---
autoDirectNodeRoutes: true
annotateK8sNode: true
bgpControlPlane:
  enabled: true
bpf:
  masquerade: false
  preallocateMaps: true
cluster:
  id: 1
  name: cp1
egressMasqueradeInterfaces: vlan20
enableIPv4Masquerade: false
enableIPv6Masquerade: true
debug:
  enabled: false
hubble:
  peerService:
    clusterDomain: cp1.jfk1.local
  preferIpv6: true
  relay:
    enabled: true
  tls:
    auto:
      enabled: true
      method: helm
      certValidityDuration: 365
  ui:
    enabled: true
ipam:
  mode: cluster-pool
  operator:
    clusterPoolIPv4MaskSize: 24
    clusterPoolIPv4PodCIDRList:
      - 10.0.0.0/16
    clusterPoolIPv6MaskSize: 80
    clusterPoolIPv6PodCIDRList:
      - 2620:a6:2001:1::/64
ipMasqAgent:
  enabled: false
ipv4:
  enabled: true
ipv4NativeRoutingCIDR: 10.0.0.0/16
ipv6NativeRoutingCIDR: 2620:a6:2001:1::/64
ipv6:
  enabled: true
k8sServiceHost: api-cp1.cnmtrcs.io
k8sServicePort: 6443
kubeProxyReplacement: "true"
l2NeighDiscovery:
  enabled: false
l7Proxy: false
loadBalancer:
  mode: dsr
#MTU: 9000
nodePort:
  directRoutingDevice: vlan30
pmtuDiscovery:
  enabled: true
policyEnforcementMode: default
routingMode: native
socketLB:
  enabled: true
  hostNamespaceOnly: true # Required for Kata: https://docs.cilium.io/en/latest/network/kubernetes/kata/
upgradeCompatibility: "1.15"
