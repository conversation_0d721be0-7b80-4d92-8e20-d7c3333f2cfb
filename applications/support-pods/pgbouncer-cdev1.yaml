---
pgBouncer:
  pgbouncer_pool_size: 3000
  auth_type: plain
  databases:
    - name: pg-api-dummy-stg-cdev1-p2
      host: pg-api-dummy-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-api-dummy-stg-cdev1-p
      host: pg-api-dummy-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-books-stg-cdev1-p
      host: pg-books-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-candles-1
      host: pg-candles-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-candles-2
      host: pg-candles-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-sharded-candles-1
      host: pg-candles-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-sharded-candles-2
      host: pg-candles-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-timeseries-candles-1
      host: pg-timeseries-candles-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-timeseries-candles-2
      host: pg-timeseries-candles-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-market-metrics-1
      host: pg-candles-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-market-metrics-2
      host: pg-candles-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-derivatives-stg-cdev1-p
      host: pg-derivatives-stg-cdev1-p-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-indices-1
      host: pg-indices-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-indices-2
      host: pg-indices-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-rates-1
      host: pg-rates-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-fh-local-stg-cdev1-p-1
      host: pg-fh-local-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-fh-local-stg-cdev1-p-2
      host: pg-fh-local-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-rates-2
      host: pg-rates-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-pprice-1
      host: pg-rates-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-pprice-2
      host: pg-rates-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-trades-derivatives-stg-cdev1-p
      host: pg-trades-derivatives-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-trades-spot-stg-cdev1-p
      host: pg-trades-spot-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-farum-1
      host: pg-farum-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-farum-2
      host: pg-farum-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-staging-legacy-postgres
      host: pg-staging-legacy-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-defi-1
      host: pg-defi-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-defi-2
      host: pg-defi-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-defi-swstg-cdev1-p-1
      host: pg-defi-swstg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

    - name: pg-airflow-stg-cdev1-p-1-0
      host: pg-airflow-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

      # ND Assets - Exporters

    - name: pg-exporter-eth1-stg-cdev1-p
      host: pg-exporter-eth-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-eth-1
      host: pg-exporter-eth-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-eth-p-1
      host: pg-exporter-eth-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-uni-stg-1
      host: pg-exporter-uni-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-flat1-stg-cdev1-p
      host: pg-exporter-uni-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-uni-p-1
      host: pg-exporter-uni-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-utxo1-stg-cdev1-p
      host: pg-exporter-utxo-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-utxo-p-1
      host: pg-exporter-utxo-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-ethcl-stg-cdev1-p-1
      host: pg-exporter-ethcl-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-ethcl-stg-cdev1-p
      host: pg-exporter-ethcl-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-exporter-cardano-stg-cdev1-p-1
      host: pg-exporter-cardano-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-python-exporters-stg-cdev1-p-1
      host: pg-python-exporters-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-python-exporters-stg-cdev1-p-2
      host: pg-python-exporters-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-farum-swstg-cdev1-p-1
      host: pg-farum-swstg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-farum-swstg-cdev1-p-2
      host: pg-farum-swstg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

      # Address Tagging

    - name: pg-tagging-pipeline-stg-cdev1-p-1
      host: pg-tagging-pipeline-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-tagging-pipeline-stg-cdev1-p-2
      host: pg-tagging-pipeline-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

      # Atlas sharded

    - name: pg-atlas-1-stg-1
      host: pg-atlas-1-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-2-stg-1
      host: pg-atlas-2-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-3-stg-1
      host: pg-atlas-3-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-4-stg-1
      host: pg-atlas-4-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-5-stg-1
      host: pg-atlas-5-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-6-stg-1
      host: pg-atlas-6-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-7-stg-1
      host: pg-atlas-7-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-8-stg-1
      host: pg-atlas-8-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-9-stg-1
      host: pg-atlas-9-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-exp-stg-1
      host: pg-atlas-exp-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-atlas-experimental-1
      host: pg-atlas-exp-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres


      # ND Metrics Factory

    - name: pg-network-data-factory-stg-1
      host: pg-network-data-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-network-data-factory-metrics-1d-1b-1m-stg-1
      host: pg-network-data-metrics-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-network-data-factory-metrics-1h-stg-1
      host: pg-network-data-metrics-1h-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

    - name: pg-nd-stg-cdev1-p-1
      host: pg-nd-factory-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

      # ND Metrics misc apps

    - name: pg-data-factory-availability-1
      host: pg-data-factory-availability-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-datavis-stg-1
      host: pg-datavis-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-network-data-flows-stg-cdev1-p-1
      host: pg-network-data-flows-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

      # Farum
      #

    - name: pg-cmf-stg-1
      host: pg-cmf-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-cmf-stg-2
      host: pg-cmf-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-mpc-stg-1
      host: pg-mpc-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-mpc-stg-2
      host: pg-mpc-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-scmf-stg-1
      host: pg-scmf-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-scmf-stg-2
      host: pg-scmf-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-spc-stg-1
      host: pg-spc-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-spc-stg-2
      host: pg-spc-stg-cdev1-a-2-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-solana-condensed-stg-cdev1-p-1
      host: pg-solana-condensed-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-solana-condensed-coord-stg-cdev1-p-1
      host: pg-solana-condensed-coord-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-solana-raw-stg-cdev1-p-1
      host: pg-solana-raw-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres
    - name: pg-fh-alerts-stg-cdev1-p
      host: pg-fh-alerts-stg-cdev1-a-1-ipv4.postgres.svc
      port: 5432
      user: postgres
      dbname: postgres

image:
  pgbouncer:
    tag: "k8s"
  copper:
    tag: "latest"

resources:
  limits:
    memory: 768Mi
  requests:
    cpu: 800m
    memory: 512Mi

tolerations:
  - key: "coinmetrics.io/zfs-only"
    operator: "Exists"
    effect: "NoExecute"
  - key: "coinmetrics.io/api4-only"
    operator: "Exists"
    effect: "NoExecute"
